{"fields": {"name": "Product Name", "price": "Price", "category": "Category", "primaryCategory": "Primary Category", "secondaryCategory": "Secondary Category", "origin": "Origin", "country": "Country", "province": "Province", "city": "City", "platform": "Platform", "specification": "Specification", "flavor": "Flavor", "manufacturer": "Manufacturer", "collectTime": "Collection Time", "sequence": "Sequence", "link": "Link", "boxSpec": "Box Specification", "notes": "Notes"}, "labels": {"normalPrice": "Normal Price", "discountPrice": "Discount Price", "discountRate": "Discount Rate", "hasDiscount": "On Sale", "noDiscount": "Regular Price", "priceRange": "Price Range", "fromPrice": "From", "toPrice": "To"}, "images": {"front": "Front Image", "back": "Back Image", "label": "Label Image", "package": "Package Image", "gift": "Gift Image", "noImage": "No Image Available", "loadingImage": "Loading Image...", "imageError": "Failed to load image"}, "filters": {"allCategories": "All Categories", "allPlatforms": "All Platforms", "allLocations": "All Locations", "showDiscountOnly": "Show Discount Only", "priceRange": "Price Range", "filterBy": "Filter <PERSON>", "sortBy": "Sort By", "clearFilters": "Clear All Filters", "applyFilters": "Apply Filters", "showDistribution": "Show Distribution", "hideDistribution": "Hide Distribution", "priceDistribution": "Price Distribution", "productCount": "{{count}} products", "minPrice": "<PERSON>", "maxPrice": "Max Price", "allPrices": "All", "title": "Filters", "activeFilters": "{{count}} active", "clear": "Clear", "resetFilters": "Reset Filters", "showingProducts": "Showing {{filtered}} / {{total}} products", "appliedFilters": "{{count}} filters applied", "noFiltersApplied": "No filters applied", "filterResults": "Filter results: {{count}} products", "viewResults": "View Results", "clearAllFilters": "Clear All Filters", "loading": "Loading filter options...", "category": {"title": "Product Category", "selected": "{{count}} selected", "total": "{{count}} categories", "items": "items", "selectedCategories": "Selected Categories", "loading": "Loading categories..."}, "platform": {"title": "Collection Platform", "selected": "{{count}} selected", "total": "{{count}} platforms", "allPlatforms": "All Platforms", "products": "{{count}} products", "selectAll": "Select All", "deselectAll": "Deselect All", "selectedPlatforms": "Selected Platforms", "statistics": "Platform Statistics", "totalPlatforms": "Total Platforms", "largestPlatform": "Largest Platform", "loading": "Loading platforms..."}, "location": {"title": "Origin Location", "selected": "{{count}} selected", "total": "{{count}} locations", "selectedLocations": "Selected Locations", "country": "Country", "province": "Province", "city": "City", "unknown": "Unknown", "loading": "Loading locations..."}, "price": {"title": "Price Range", "currency": "<PERSON><PERSON><PERSON><PERSON>", "cny": "CNY", "usd": "USD", "all": "All Prices", "custom": "Custom Range", "to": "to", "apply": "Apply", "distribution": "Price Distribution", "showDistribution": "Show Distribution", "hideDistribution": "Hide Distribution", "loading": "Loading price range..."}}, "sorting": {"name": "Sort by name", "priceAsc": "Price: Low to High", "priceDesc": "Price: High to Low", "collectTime": "By collection time", "latest": "Latest First", "oldest": "Oldest First"}, "actions": {"viewDetails": "View Details", "quickView": "Quick View", "favorite": "Add to Favorites", "favorited": "Favorited", "compare": "Compare", "compared": "In Comparison", "share": "Share", "copyLink": "Copy Link", "back": "Back", "backToList": "Back to Product List"}, "stats": {"totalProducts": "Total Products", "averagePrice": "Average Price", "priceRange": "Price Range", "categories": "Categories", "platforms": "Platforms", "locations": "Locations"}, "detail": {"title": "Product Detail", "pageTitle": "Product Showcase System", "breadcrumb": {"list": "Product List", "detail": "Product Detail"}, "loading": "Loading product information...", "notFound": {"title": "Product Not Found", "message": "Sorry, the product you are looking for does not exist or has been deleted", "backButton": "Back to Product List"}, "errors": {"loadFailed": "Failed to load product", "invalidId": "Invalid product ID"}, "share": {"text": "Check out this product: {{productName}}", "linkCopied": "Link copied to clipboard"}, "basicInfo": {"title": "Basic Information"}, "toast": {"favoriteAdded": "Added to favorites", "favoriteRemoved": "Removed from favorites", "compareAdded": "Added to comparison list", "compareRemoved": "Removed from comparison list", "compareLimit": "Comparison list can only contain up to 4 products"}, "defaultValues": {"noData": "N/A"}}, "info": {"sections": {"price": "Price Information", "category": "Category Information", "sales": "Sales Information", "origin": "Origin Information"}, "labels": {"normalPrice": "Regular Price", "discountPrice": "Discount Price", "discountRate": "Discount Rate", "savings": "Savings", "primaryCategory": "Primary Category", "secondaryCategory": "Secondary Category", "platform": "Platform", "manufacturer": "Manufacturer", "productLink": "Product Link", "country": "Country", "province": "Province", "city": "City", "viewLink": "View Link"}}, "related": {"title": "Related Products", "subtitle": "Based on category, price, platform and other factors, we recommend {{count}} related products for you", "noProducts": {"title": "No Related Products", "message": "No related products found for the current item"}, "reasons": {"sameCategory": "Same Category", "similarCategory": "Similar Category", "similarPrice": "Similar Price", "nearPrice": "Near Price", "samePlatform": "Same Platform", "sameOrigin": "Same Origin", "hasDiscount": "On Sale", "related": "Related"}, "algorithm": {"title": "Recommendation Algorithm", "strategies": {"category": "Same Category Recommendation", "categoryDesc": "Products from the same category", "price": "Similar Price", "priceDesc": "Products with similar price range", "platform": "Same Platform Recommendation", "platformDesc": "Products from the same platform", "origin": "Same Origin Recommendation", "originDesc": "Products from the same origin", "discount": "Discount Products", "discountDesc": "Products with discounts"}}}}